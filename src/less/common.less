@import 'variables';
@import 'mediaMixin.less';
// grid
@import './grid.less';

@import './reset';

@import './iconfont.less';
@import './button.less';
@import './base.less';

// components
@import '../components/input/input.less';
@import '../components/dropdown-select/index.less';
@import '../components/checkBox/checkbox.less';
@import '../components/bubble/bubble.less';

// plugins
@import '../plugins/citySelect/citySelect.less';
@import '../plugins/datepicker/datepicker.less';

// 公共模块
@import '../modules/header/header.less';
@import '../modules/footer/footer.less';
@import '../modules/codeModule/index.less';
@import '../modules/privacySetting/index.less';
@import '../modules/step/index.less'; // 步骤条

// ---------------------------------------------------------------------------------------------------------------

.sz {
  min-height: 100vh;

  &-banner {
    width: 100%;
    height: 380px;
    position: relative;
    z-index: -1;
    object-fit: cover;

    .screenPad({
      height: 265px;
      });

    .screenMobile({
      height: 130px;
    });
  }

  &-banner_min {
    width: 100%;
    height: 260px;
    position: relative;
    z-index: -1;
    object-fit: cover;

    .screenPad({
      height: 250px;
      });

    .screenMobile({
      height: 225px;
    });
  }

  &-container {
    max-width: 1380px;
    margin: -80px auto 136px;

    .screenPad({
       padding: 0 20px;
      });

    .screenMobile({
      padding: 0 15px;
    });
  }
}
