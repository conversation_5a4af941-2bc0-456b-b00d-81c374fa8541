<div class="page-container">
  <div class="section flight-summary-section">
    <div class="section-header">
      <div class="flight-route">
        <span class="city"
          ><%= data.payment.flightSummary.departureCity %></span
        >
        <i class="fas fa-arrow-right"></i>
        <span class="city"><%= data.payment.flightSummary.arrivalCity %></span>
      </div>
      <div class="flight-times">
        <span class="time"
          ><%= data.payment.flightSummary.departureTime %></span
        >
        -
        <span class="time"><%= data.payment.flightSummary.arrivalTime %></span>
      </div>
    </div>
    <div class="change-options">
      <div class="option-item">
        <label>Change Flight</label>
        <div class="value">
          <a href="#">Select Flight</a> <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      <div class="option-item">
        <label>Change of Flight/Cabin Class</label>
        <div class="value">
          <span class="selected-class"
            ><%= data.payment.flightSummary.cabinClass %></span
          >
          <a href="#">Select Cabin</a> <i class="fas fa-chevron-right"></i>
        </div>
      </div>
      <div class="option-item">
        <label>Fare Rule</label>
        <div class="value">
          <a href="#"><%= data.payment.flightSummary.fareRule %></a>
        </div>
      </div>
      <div class="option-item">
        <label>Refund Rule</label>
        <div class="value">
          <a href="#"><%= data.payment.flightSummary.refundRule %></a>
        </div>
      </div>
      <div class="option-item">
        <label>Change Rule</label>
        <div class="value">
          <a href="#"><%= data.payment.flightSummary.changeRule %></a>
        </div>
      </div>
      <div class="option-item">
        <label>Baggage Rule</label>
        <div class="value">
          <a href="#"><%= data.payment.flightSummary.baggageRule %></a>
        </div>
      </div>
    </div>
    <div class="additional-info">
      <i class="fas fa-exclamation-circle info-icon"></i>
      <span><%= data.payment.flightSummary.additionalInfo %></span>
    </div>
  </div>

  <div class="section passenger-info-section">
    <div class="section-title">Passenger information</div>
    <div class="passenger-table-container">
      <table class="passenger-table">
        <thead>
          <tr>
            <th>Passenger Name</th>
            <th>ID Type</th>
            <th>ID No.</th>
            <th>Nationality</th>
            <th>Birthday</th>
            <th>Ticket Number</th>
            <th>Contact Phone</th>
            <th>Contact Email</th>
            <th>Baggage Weight</th>
            <th>Meal Preference</th>
            <th>Remark</th>
          </tr>
        </thead>
        <tbody>
          <% data.payment.passengers.forEach(passenger => { %>
          <tr>
            <td><%= passenger.name %></td>
            <td><%= passenger.idType %></td>
            <td><%= passenger.idNumber %></td>
            <td><%= passenger.nationality %></td>
            <td><%= passenger.birthday %></td>
            <td><%= passenger.ticketNumber %></td>
            <td><%= passenger.contactPhone %></td>
            <td><%= passenger.contactEmail %></td>
            <td><%= passenger.baggageWeight %></td>
            <td><%= passenger.mealPreference %></td>
            <td><%= passenger.remark %></td>
          </tr>
          <% }); %>
        </tbody>
      </table>
    </div>
  </div>

  <div class="section additional-services-section">
    <div class="section-title">Additional services</div>

    <div class="service-item">
      <div class="service-header">
        <i class="fas fa-chair service-icon"></i>
        <h3>Pre-paid seat selection</h3>
        <a href="#" class="add-btn"><i class="fas fa-plus"></i> Add</a>
      </div>
      <% data.payment.additionalServices.seatSelection.forEach(seat => { %>
      <div class="service-detail-row">
        <span><%= seat.flight %></span>
        <span><%= seat.status %></span>
        <span class="service-price"><%= seat.price %></span>
      </div>
      <% }); %>
    </div>

    <div class="service-item">
      <div class="service-header">
        <i class="fas fa-suitcase-rolling service-icon"></i>
        <h3>Special luggage</h3>
        <a href="#" class="add-btn"><i class="fas fa-plus"></i> Add</a>
      </div>
      <div class="service-detail-row">
        <span
          ><%= data.payment.additionalServices.specialLuggage.status %></span
        >
        <span class="service-price"
          ><%= data.payment.additionalServices.specialLuggage.price %></span
        >
      </div>
    </div>

    <div class="service-item">
      <div class="service-header">
        <i class="fas fa-shield-alt service-icon"></i>
        <h3>Pre-paid insurance</h3>
        <a href="#" class="add-btn"><i class="fas fa-plus"></i> Add</a>
      </div>
      <div class="service-detail-row">
        <span
          ><%= data.payment.additionalServices.prepaidInsurance.status %></span
        >
        <span class="service-price"
          ><%= data.payment.additionalServices.prepaidInsurance.price %></span
        >
      </div>
    </div>
  </div>

  <div class="section contact-info-section">
    <div class="section-title">Contact Information</div>
    <div class="contact-details">
      <div class="detail-row">
        <label>Contact Name</label>
        <span><%= data.payment.contactInfo.contactName %></span>
      </div>
      <div class="detail-row">
        <label>Contact Phone</label>
        <span><%= data.payment.contactInfo.contactPhone %></span>
      </div>
      <div class="detail-row">
        <label>Contact Email</label>
        <span><%= data.payment.contactInfo.contactEmail %></span>
      </div>
      <div class="detail-row">
        <label>Invoice</label>
        <span><%= data.payment.contactInfo.invoice %></span>
      </div>
      <div class="detail-row">
        <label>Invoice Email</label>
        <span><%= data.payment.contactInfo.invoiceEmail %></span>
      </div>
    </div>
  </div>

  <div class="section payment-method-section">
    <div class="section-title">Select Payment Method</div>
    <div class="payment-note">
      <i class="fas fa-info-circle info-icon"></i>
      <span
        >To protect your financial security, please complete payment within 30
        minutes, otherwise your reservation will be canceled.</span
      >
    </div>
    <div class="payment-tabs">
      <% data.payment.paymentMethods.forEach(method => { %>
      <div
        class="payment-tab <%= method.active ? 'active' : '' %>"
        data-payment-id="<%= method.id %>"
      >
        <%= method.name %>
      </div>
      <% }); %>
    </div>
    <div class="payment-content active" id="payment_qr_code">
      <div class="qr-code-placeholder">
        <i class="fas fa-qrcode qr-icon"></i>
        <span>Scan to Pay</span>
        <button class="pay-button">CNY 20 Payment</button>
      </div>
    </div>
    <div class="payment-content" id="payment_international_card">
      <p>International Card Payment details would go here.</p>
    </div>
    <div class="payment-content" id="payment_credit_card">
      <p>Credit Card Payment details would go here.</p>
    </div>
    <div class="payment-content" id="payment_bank_card">
      <p>Bank Card Payment details would go here.</p>
    </div>
    <div class="payment-content" id="payment_third_party">
      <p>Third Party Payment details would go here.</p>
    </div>
  </div>

  <div class="section collect-itinerary-section">
    <div class="section-title">Collect Itinerary</div>
    <div class="itinerary-options">
      <% data.payment.collectItinerary.options.forEach((option, index) => { %>
      <div class="itinerary-option">
        <input type="radio" id="itinerary_option_<%= index %>"
        name="itinerary_option" <%= index === 0 ? 'checked' : '' %>>
        <label for="itinerary_option_<%= index %>"><%= option.text %></label>
      </div>
      <% }); %>
    </div>
  </div>
</div>

<div class="total-cost-footer">
  <div class="total-cost-label">Total cost:</div>
  <div class="total-cost-value"><%= data.payment.totalCost %></div>
  <button class="confirm-payment-btn">Confirm Payment</button>
</div>
