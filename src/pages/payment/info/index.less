@primary-red: #e74c3c;
@dark-gray: #333;
@medium-gray: #555;
@light-gray: #777;
@border-color: #eee;
@bg-light: #f0f2f5;
@card-bg: #fff;
@font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

.page-container {
  max-width: 960px;
  margin: 20px auto;
  padding: 0 15px; // Add some padding on sides for smaller screens
}

.section {
  background-color: @card-bg;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px; // Default padding for all sections
}

.section-title {
  font-size: 1.2em;
  font-weight: bold;
  color: @dark-gray;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid @border-color;
}

// Section 1: Flight Summary
.flight-summary-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid @border-color;
    padding-bottom: 15px;

    .flight-route {
      font-size: 1.8em;
      font-weight: bold;
      color: @dark-gray;

      .city {
        margin: 0 5px;
      }
      .fa-arrow-right {
        font-size: 0.8em;
        color: @light-gray;
      }
    }

    .flight-times {
      font-size: 1.2em;
      color: @medium-gray;
      .time {
        font-weight: bold;
      }
    }
  }

  .change-options {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(280px, 1fr)
    ); // Responsive 2 or 3 columns
    gap: 15px 30px; // Row gap, column gap
    margin-bottom: 20px;

    .option-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px dashed lighten(@border-color, 5%);

      &:last-child {
        border-bottom: none;
      }

      label {
        font-size: 0.9em;
        color: @light-gray;
        white-space: nowrap; // Prevent wrapping for labels
        margin-right: 10px;
      }

      .value {
        font-size: 0.95em;
        color: @dark-gray;
        font-weight: 500;
        text-align: right;

        a {
          color: #007bff; // Standard link blue
          text-decoration: none;
          margin-left: 5px;

          &:hover {
            text-decoration: underline;
          }
        }
        .fa-chevron-right {
          font-size: 0.7em;
          color: @light-gray;
          margin-left: 5px;
        }
        .selected-class {
          font-weight: bold;
          color: @dark-gray;
        }
      }
    }
  }

  .additional-info {
    background-color: #fcf8e3; // Light yellow background
    border: 1px solid #faebcc; // Slightly darker yellow border
    border-radius: 5px;
    padding: 15px;
    font-size: 0.9em;
    color: #8a6d3b; // Dark yellow text
    display: flex;
    align-items: flex-start;

    .info-icon {
      color: #f7bb2a; // Bright yellow icon
      margin-right: 10px;
      font-size: 1.2em;
      flex-shrink: 0;
    }
  }
}

// Section 2: Passenger Information
.passenger-info-section {
  .passenger-table-container {
    overflow-x: auto; // Enable horizontal scrolling for narrow screens
  }

  .passenger-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    min-width: 900px; // Ensure table doesn't collapse too much on smaller screens

    th,
    td {
      border: 1px solid @border-color;
      padding: 10px 12px;
      text-align: left;
      white-space: nowrap; // Prevent text from wrapping in cells
    }

    th {
      background-color: #f8f8f8;
      font-weight: bold;
      color: @medium-gray;
    }

    tbody tr:nth-child(even) {
      background-color: #fcfcfc;
    }
    tbody tr:hover {
      background-color: #f5f5f5;
    }
  }
}

// Section 3: Additional Services
.additional-services-section {
  .service-item {
    margin-bottom: 25px;
    border: 1px solid @border-color;
    border-radius: 5px;
    padding: 15px;
    background-color: #fcfcfc;

    &:last-child {
      margin-bottom: 0;
    }

    .service-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dashed lighten(@border-color, 5%);

      .service-icon {
        font-size: 1.5em;
        color: @medium-gray;
        margin-right: 15px;
      }

      h3 {
        flex-grow: 1;
        margin: 0;
        font-size: 1.1em;
        color: @dark-gray;
      }

      .add-btn {
        background-color: #f0f0f0;
        color: @light-gray;
        padding: 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 0.9em;
        display: flex;
        align-items: center;

        &:hover {
          background-color: #e0e0e0;
        }
        .fa-plus {
          margin-right: 5px;
        }
      }
    }

    .service-detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.9em;
      color: @medium-gray;
      padding: 5px 0;

      .service-price {
        font-weight: bold;
        color: @dark-gray;
      }
    }
  }
}

// Section 4: Contact Information
.contact-info-section {
  .contact-details {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(280px, 1fr)
    ); // Responsive grid
    gap: 15px 30px;

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px dashed lighten(@border-color, 5%);

      label {
        font-size: 0.9em;
        color: @light-gray;
        margin-right: 15px;
        white-space: nowrap;
      }
      span {
        font-size: 0.95em;
        color: @dark-gray;
        font-weight: 500;
        text-align: right;
      }
    }
  }
}

// Section 5: Payment Method
.payment-method-section {
  .payment-note {
    background-color: #fefbeb; // Light yellow
    border: 1px solid #f9e2b1; // Slightly darker yellow
    border-radius: 5px;
    padding: 10px 15px;
    font-size: 0.9em;
    color: #8b6b27; // Dark yellow text
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;

    .info-icon {
      color: #f7d04e; // Brighter yellow icon
      margin-right: 10px;
      font-size: 1.1em;
      flex-shrink: 0;
    }
  }

  .payment-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
    flex-wrap: wrap; // Allow tabs to wrap on smaller screens

    .payment-tab {
      padding: 12px 20px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      font-size: 0.95em;
      color: @medium-gray;
      background-color: #f9f9f9;
      margin-right: 5px; // Small gap between tabs
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      margin-bottom: -1px; // Overlap border with content below

      &:hover {
        background-color: #efefef;
      }

      &.active {
        background-color: @card-bg;
        border-color: #ddd;
        border-bottom: 1px solid @card-bg; // Hide bottom border visually
        color: @dark-gray;
        font-weight: bold;
      }
    }
  }

  .payment-content {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    border-top-left-radius: 0; // Adjust for tab visual
    background-color: @card-bg;
    display: none; // Hidden by default, JS will show active
    min-height: 100px; // Ensure content area is visible

    &.active {
      display: block;
    }

    .qr-code-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 150px;
      background-color: #f0f2f5;
      border-radius: 8px;
      padding: 20px;

      .qr-icon {
        font-size: 4em;
        color: #4caf50; // Green for QR code
        margin-bottom: 15px;
      }
      span {
        font-size: 1.1em;
        color: @medium-gray;
        margin-bottom: 20px;
      }
      .pay-button {
        background-color: @primary-red;
        color: #fff;
        border: none;
        padding: 12px 25px;
        border-radius: 5px;
        font-size: 1.1em;
        cursor: pointer;
        &:hover {
          background-color: darken(@primary-red, 10%);
        }
      }
    }
  }
}

// Section 6: Collect Itinerary
.collect-itinerary-section {
  .itinerary-options {
    .itinerary-option {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      input[type='radio'] {
        margin-top: 4px; // Align radio button with text
        margin-right: 10px;
        flex-shrink: 0; // Prevent shrinking
      }

      label {
        font-size: 0.95em;
        color: @medium-gray;
        line-height: 1.4;
        cursor: pointer;
      }
    }
  }
}

// Floating Total Cost Footer
.total-cost-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: @dark-gray;
  color: #fff;
  padding: 15px 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000; // Ensure it stays on top of other content

  .total-cost-label {
    font-size: 1.1em;
    margin-right: 10px;
    color: #ccc;
  }

  .total-cost-value {
    font-size: 1.6em;
    font-weight: bold;
    color: #ffda44; // Yellow for total cost
    margin-right: 30px;
  }

  .confirm-payment-btn {
    background-color: @primary-red;
    color: #fff;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    &:hover {
      background-color: darken(@primary-red, 10%);
    }
  }
}
