@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.horizontal-price-calendar {
  display: flex;

  &-left {
    flex: 1;
    display: flex;
    align-items: center;

    &-content {
      flex: 1;
      padding: 0 18px;
      display: flex;

      .screenMobile({
        padding: 0 2px;
      });
    }
  }

  &-lr_btn {
    border-radius: 0;

    &-img {
      width: 15px;
    }

    &.disabled {
      cursor: not-allowed;
      background: @gray-1;
    }
  }

  &-right {
    margin-left: 18px;

    display: flex;
    flex-direction: column;
    align-items: center;

    &-img {
      width: 24px;
    }

    &-text {
      margin-top: 4px;
      font-size: 16px;
      color: @sub-3;

      .screenMobile({
        display: none;
      });
    }
  }

  &-wrap_item {
    flex: 1;
    border-left: 1px solid @sub-2;
    padding: 0 6px;

    &-no_bl {
      border-left: none;
    }

    // PC端显示7个元素（小屏PC和大屏PC都显示）
    &-pc {
      display: flex;

      // 在pad端和移动端隐藏PC版本
      .screenPad({
        display: none;
      });

      .screenMobile({
        display: none;
      });
    }

    // Pad端和移动端显示3个元素
    &-mobile {
      display: none; // 默认隐藏，只在pad和移动端显示

      // 在pad端显示
      .screenPad({
        display: flex;
      });

      // 在移动端显示
      .screenMobile({
        display: flex;
      });
    }
  }

  &-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    padding-top: 8px;
    padding-bottom: 8px;
    border-radius: 8px;
    border: 1px solid transparent;

    &-unit {
      font-size: 16px;
      color: @gray-5;

      .screenMobile({
         font-size: 12px;
      });
    }

    &-amount {
      font-size: 20px;
      color: @gray-5;

      .screenMobile({
         font-size: 12px;
      });
    }

    &-date,
    &-week {
      font-size: 16px;
      color: @gray-3;

      .screenMobile({
         font-size: 12px;
      });
    }

    &-week {
      .screenMobile({
        display: none;
      });
    }

    &:hover {
      border: 1px solid @brand-1;
    }

    &.active {
      border: 1px solid @brand-1;

      & span {
        color: @brand-1 !important;
      }
    }
  }
}
