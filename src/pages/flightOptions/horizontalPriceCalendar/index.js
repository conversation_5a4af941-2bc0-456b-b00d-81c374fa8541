// 全局变量，用于跟踪当前显示的日期范围
let currentStartDay = 0 // 从今天开始的偏移量
let calendarItems = [] // 存储日历项数据
let items = [] // 存储DOM元素和数据
let selectedDate = null // 存储当前选中的日期

// 获取当前设备应该显示的天数
function getDaysToShow() {
  const screenWidth = window.innerWidth
  // 根据媒体查询断点判断设备类型，使用全局常量
  if (screenWidth <= window.SCREEN_BREAKPOINTS.MOBILE_MAX) {
    // 移动端显示3个
    return 3
  } else if (
    screenWidth >= window.SCREEN_BREAKPOINTS.PAD_MIN &&
    screenWidth <= window.SCREEN_BREAKPOINTS.PAD_MAX
  ) {
    // Pad端显示3个
    return 3
  } else {
    // PC端（包括小屏PC和大屏PC）显示7个
    return 7
  }
}

// 获取当前设备类型对应的ID前缀
function getItemIdPrefix() {
  const screenWidth = window.innerWidth
  if (screenWidth <= window.SCREEN_BREAKPOINTS.PAD_MAX) {
    // Pad端和移动端使用mobile版本
    return 'horizontal-price-calendar-item-mobile-'
  } else {
    // PC端（包括小屏PC和大屏PC）使用默认版本
    return 'horizontal-price-calendar-item-'
  }
}

// 将函数定义在全局作用域
function goHorizontalPriceCalendarPrev() {
  const daysToShow = getDaysToShow()

  // 如果当前已经是第一页（从今天开始），则不执行操作
  if (currentStartDay <= 0) {
    // console.log('已经是第一页，无法向前翻页')
    return
  }

  // 向前翻页，更新起始日偏移量
  currentStartDay -= daysToShow

  // 如果向前翻页后起始日小于0，则设为0（即从今天开始）
  if (currentStartDay < 0) {
    currentStartDay = 0
  }

  // 更新日历数据和UI
  updateCalendarItems()
  updateButtonStatus()
}

function goHorizontalPriceCalendarNext() {
  const daysToShow = getDaysToShow()

  // console.log('向后翻页')
  // 向后翻页，更新起始日偏移量
  currentStartDay += daysToShow

  // 更新日历数据和UI
  updateCalendarItems()
  updateButtonStatus()
}

// 更新按钮状态
function updateButtonStatus() {
  const $prevBtn = $('.horizontal-price-calendar-left button:first-child')
  const $nextBtn = $('.horizontal-price-calendar-left button:last-child')

  // 如果当前是第一页，禁用向前按钮
  if (currentStartDay <= 0) {
    $prevBtn.addClass('disabled')
  } else {
    $prevBtn.removeClass('disabled')
  }

  // 这里可以添加向后翻页的限制逻辑，例如最多只能查看未来60天
  // if (currentStartDay >= 60 - daysToShow) {
  //   $nextBtn.addClass('disabled');
  // } else {
  //   $nextBtn.removeClass('disabled');
  // }
}

// 更新日历项数据和UI
function updateCalendarItems() {
  const daysToShow = getDaysToShow()
  const idPrefix = getItemIdPrefix()

  // 清空原有数组
  calendarItems = []

  // 获取今天的日期
  const today = dayjs()

  // 创建新的日历项数据
  for (let i = 1; i <= daysToShow; i++) {
    // 计算当前项对应的日期（今天 + currentStartDay + i - 1天）
    const currentDate = today.add(currentStartDay + i - 1, 'day')
    const dateStr = currentDate.format('YYYY-MM-DD') // 用于比较的完整日期字符串

    // 创建包含所有信息的对象
    calendarItems.push({
      id: `#${idPrefix}${i}`,
      date: currentDate.format('MM-DD'), // 格式如：06-15
      week: currentDate.format('ddd'), // 格式如：Sun
      amount: Math.floor(1500 + Math.random() * 1000), // 随机金额1500-2500
      currency: 'CNY',
      fullDate: dateStr, // 存储完整日期用于比较
    })
  }

  // 更新UI
  items = calendarItems.map((item) => {
    const $element = $(item.id)

    // 更新日历项的内容
    $element.find('.horizontal-price-calendar-item-unit').text(item.currency)
    $element.find('.horizontal-price-calendar-item-amount').text(item.amount)
    $element.find('.horizontal-price-calendar-item-date').text(item.date)
    $element.find('.horizontal-price-calendar-item-week').text(item.week)

    // 如果当前日期与选中日期匹配，添加active类
    if (selectedDate && item.fullDate === selectedDate) {
      $element.addClass('active')
    } else {
      $element.removeClass('active')
    }

    return { $element, ...item }
  })
}

;+(function ($) {
  // 初始化日历数据
  updateCalendarItems()

  // 初始化按钮状态
  updateButtonStatus()

  // 为每个日历项添加点击事件
  $(document).on('click', '.horizontal-price-calendar-item', function () {
    const currentId = $(this).attr('id')
    let index = -1

    // 根据不同的ID格式解析索引
    if (currentId.includes('horizontal-price-calendar-item-mobile-')) {
      index =
        currentId.replace('horizontal-price-calendar-item-mobile-', '') - 1
    } else if (currentId.includes('horizontal-price-calendar-item-')) {
      index = currentId.replace('horizontal-price-calendar-item-', '') - 1
    }

    // 移除所有项的active类
    $('.horizontal-price-calendar-item').removeClass('active')

    // 为当前点击项添加active类
    $(this).addClass('active')

    // 获取对应的数据项
    if (index >= 0 && index < calendarItems.length) {
      const item = calendarItems[index]

      // 保存选中的日期
      selectedDate = item.fullDate

      // console.log(
      //   `选择了第${index + 1}天，日期：${item.date}，星期：${item.week}，价格：${
      //     item.currency
      //   } ${item.amount}`
      // )
    }
  })

  // 监听窗口大小变化，重新初始化日历
  let resizeTimer
  $(window).on('resize', function () {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(function () {
      // 重置当前起始日，避免在切换设备时出现显示问题
      currentStartDay = 0
      updateCalendarItems()
      updateButtonStatus()
    }, 250) // 防抖处理，250ms后执行
  })
})(jQuery)
